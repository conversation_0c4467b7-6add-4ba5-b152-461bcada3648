import { test, expect } from '@playwright/test';
import { emailMock } from '../setup/global-setup';

test.describe('Organization member invitation with email mocking', () => {
	const testOrgName = `Test Org ${Date.now()}`;
	const testEmail = `test-${Date.now()}@example.com`;
	const testPassword = 'TestPassword123';

	test.beforeAll(async ({ browser }) => {
		// Create a test user and organization first
		const page = await browser.newPage();

		// Setup email mocking for this page
		await emailMock.setupEmailMocking(page);

		await page.goto('/auth/signup');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Wait for signup success message
		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Should be redirected to org creation since user has no orgs
		await page.waitForURL(/\/org\/new/, { timeout: 10000 });

		// Create organization
		await page.fill('input[name="name"]', testOrgName);
		await page.click('button[type="submit"]');

		// Wait for redirect to clients page
		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 10000 });
		await page.close();
	});

	test.beforeEach(async ({ page }) => {
		// Setup email mocking for each test
		await emailMock.setupEmailMocking(page);

		// Clear previous email calls
		emailMock.clearEmailCalls();

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Navigate to invite page
		await page.goto(`/org/${encodeURIComponent(testOrgName)}/invite`);
	});

	test('should successfully invite a new member and capture email', async ({ page }) => {
		const inviteEmail = '<EMAIL>';

		// Fill the form with valid data
		await page.fill('input[name="email"]', inviteEmail);

		// Submit the form
		const submitButton = page.locator('button[type="submit"]');
		await expect(submitButton).toBeVisible();
		await submitButton.click();

		// Wait for form submission to complete
		await page.waitForLoadState('networkidle');

		// The most important part: verify email was captured by the mock
		expect(emailMock.getEmailCallCount()).toBe(1);

		const emailCall = emailMock.getLastEmailCall();
		expect(emailCall).not.toBeNull();
		expect(emailCall!.to).toBe(inviteEmail);
		expect(emailCall!.subject).toContain('invited to join');
		expect(emailCall!.html).toContain('You have been invited');
		expect(emailCall!.html).toContain('/auth/invite/');

		// Alternative assertion method using the built-in assert function
		emailMock.assertEmailSent({
			to: inviteEmail,
			subject: /invited to join.*organization/i,
			containsHtml: 'You have been invited',
		});
	});

	test('should handle email service failures gracefully', async ({ page }) => {
		// Configure the mock to simulate email failures
		emailMock.setEmailFailure(true);

		const inviteEmail = '<EMAIL>';

		// Fill the form with valid data
		await page.fill('input[name="email"]', inviteEmail);

		// Note: Role defaults to 'member' so no need to select it explicitly for this test

		// Submit the form
		const submitButton = page.locator('button[type="submit"]');
		await expect(submitButton).toBeVisible();
		await submitButton.click();

		// Wait for error toast message
		await expect(
			page.locator('[data-sonner-toast]').filter({ hasText: /Failed to send invitation email/i }),
		).toBeVisible({
			timeout: 10000,
		});

		// Verify that the email was still captured (even though it "failed")
		expect(emailMock.getEmailCallCount()).toBe(1);
		expect(emailMock.wasEmailSentTo(inviteEmail)).toBe(true);

		// Reset email failure for other tests
		emailMock.setEmailFailure(false);
	});

	test('should allow admin role selection and capture appropriate email', async ({ page }) => {
		const inviteEmail = '<EMAIL>';

		// Fill the form with valid data
		await page.fill('input[name="email"]', inviteEmail);

		// Select admin role using the Select component - wait for dropdown to appear
		await page.locator('[data-slot="select-trigger"]').click();
		await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
		await page.locator('[data-slot="select-item"][data-value="admin"]').click();

		// Submit the form
		const submitButton = page.locator('button[type="submit"]');
		await expect(submitButton).toBeVisible();
		await submitButton.click();

		// Wait for success message
		await expect(page.locator('text=/invitation sent/i')).toBeVisible({ timeout: 10000 });

		// Verify email was captured
		const emailCall = emailMock.getLastEmailCall();
		expect(emailCall).not.toBeNull();
		expect(emailCall!.to).toBe(inviteEmail);

		// Verify the email content is appropriate for organization invitation
		expect(emailCall!.subject).toContain('invited to join the organization');
	});

	test('should capture multiple email invitations', async ({ page }) => {
		const emails = ['<EMAIL>', '<EMAIL>'];

		for (const email of emails) {
			// Fill the form
			await page.fill('input[name="email"]', email);

			// Select role - wait for dropdown to appear
			await page.locator('[data-slot="select-trigger"]').click();
			await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
			await page.locator('[data-slot="select-item"][data-value="member"]').click();

			// Submit
			const submitButton = page.locator('button[type="submit"]');
			await submitButton.click();

			// Wait for success
			await expect(page.locator('text=/invitation sent/i')).toBeVisible({ timeout: 10000 });

			// Clear the form for next iteration
			await page.fill('input[name="email"]', '');
		}

		// Verify both emails were captured
		expect(emailMock.getEmailCallCount()).toBe(2);

		for (const email of emails) {
			expect(emailMock.wasEmailSentTo(email)).toBe(true);
		}

		// Verify we can find emails by recipient
		const user1Emails = emailMock.getEmailCallsTo('<EMAIL>');
		expect(user1Emails).toHaveLength(1);

		const user2Emails = emailMock.getEmailCallsTo('<EMAIL>');
		expect(user2Emails).toHaveLength(1);
	});

	test('should demonstrate async email waiting', async ({ page }) => {
		const inviteEmail = '<EMAIL>';

		// Fill and submit form
		await page.fill('input[name="email"]', inviteEmail);
		await page.locator('[data-slot="select-trigger"]').click();
		await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
		await page.locator('[data-slot="select-item"][data-value="member"]').click();

		const submitButton = page.locator('button[type="submit"]');
		await submitButton.click();

		// Use the async email waiting feature
		const emailCall = await emailMock.waitForEmail(
			(call) => call.to === inviteEmail,
			5000, // 5 second timeout
		);

		expect(emailCall).not.toBeNull();
		expect(emailCall!.to).toBe(inviteEmail);
	});
});
