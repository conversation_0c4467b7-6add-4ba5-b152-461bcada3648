import { expect, test } from '@playwright/test';
import { emailMock } from '../setup/global-setup';

// Test fixtures for organization auth flows
test.describe('Organization Auth E2E Tests', () => {
	// Generate unique identifiers for testing
	const testEmail = `test-${Date.now()}@example.com`;
	const testPassword = 'TestPassword123';
	const testOrgName = `Test Org ${Date.now()}`;

	// This test should run first to create a user that will be used in subsequent tests
	test.beforeAll(async ({ browser }) => {
		// Set up a test user for organization tests
		const page = await browser.newPage();

		// Try to sign up (this will succeed only if the user doesn't exist)
		await page.goto('/auth/signup');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Wait for either success message or redirect to /
		await Promise.race([
			page.waitForURL(/\//, { timeout: 10000 }),
			page.waitForSelector('text=Welcome to ', { timeout: 10000 }),
		]);

		// Now sign in if we're still on the auth page
		if (page.url().includes('/auth/')) {
			await page.goto('/auth/signin');
			await page.fill('input[name="email"]', testEmail);
			await page.fill('input[name="password"]', testPassword);
			await page.click('button[type="submit"]');

			// Wait for redirect to organization creation page
			await page.waitForURL(/\//, { timeout: 10000 });
		}

		await page.close();
	});

	test('create organization and verify in database', async ({ page }) => {
		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		await page.waitForURL(/\//, { timeout: 3000 });
		await page.goto('/org/new', { timeout: 3000 });

		// Fill organization details
		await page.fill('input[name="name"]', testOrgName);

		// Optional fields if they exist
		try {
			await page.fill('textarea[name="description"]', 'Test organization description');
		} catch (e) {
			// Description field might be optional or not shown
		}

		// Submit the form
		await page.click('button[type="submit"]');

		// Should be redirected to the clients page or dashboard
		await expect(page).toHaveURL(/clients\/new/, { timeout: 10000 });

		// Verify organization name appears in the interface
		await expect(page.locator(`text=${testOrgName}`)).toBeVisible({ timeout: 5000 });
	});

	test('invite members with different roles', async ({ page }) => {
		// Setup email mocking
		await emailMock.setupEmailMocking(page);
		emailMock.clearEmailCalls();

		// Sign in if not already signed in
		if (!page.url().includes('/client')) {
			await page.goto('/auth/signin');
			await page.fill('input[name="email"]', testEmail);
			await page.fill('input[name="password"]', testPassword);
			await page.click('button[type="submit"]');
			await page.waitForURL(/\//, { timeout: 10000 });
		}

		// Go to organization members or settings page
		// The actual URL path will depend on your application structure
		await page.goto(`/org/${encodeURIComponent(testOrgName)}/members`);

		// Look for an invite button
		await page.getByRole('link', { name: 'Add New Teammate' }).click();
		// await page.getByRole('button, a', { hasText: /invite|add new teammate/i }).click();

		// Fill the invite form
		const inviteEmail = `invite-${Date.now()}@example.com`;
		await page.fill('input[name="email"]', inviteEmail);

		// Select role
		await page.locator('[data-slot="select-trigger"]').click();
		await page.waitForSelector('[data-slot="select-content"]', { state: 'visible' });
		await page.locator('[data-slot="select-item"][data-value="member"]').click();

		// Submit the invitation
		await page.click('button[type="submit"]');

		// Verify success message
		await expect(page.locator('text=/invited|invitation sent/')).toBeVisible({ timeout: 5000 });

		// Verify email was sent using the mock
		expect(emailMock.getEmailCallCount()).toBe(1);

		const emailCall = emailMock.getLastEmailCall();
		expect(emailCall).not.toBeNull();
		expect(emailCall!.to).toBe(inviteEmail);
		expect(emailCall!.subject).toContain('invited to join the organization');

		// Verify member appears in members list (might need to refresh)
		await page.reload();
		await expect(page.locator(`text=${inviteEmail}`)).toBeVisible({ timeout: 5000 });
	});

	test('switch between organizations', async ({ page }) => {
		// This test assumes the user has already created one organization

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Create a second organization
		const secondOrgName = `Second Org ${Date.now()}`;

		// Navigate to create new organization
		await page.goto('/org/new');

		// Fill organization details
		await page.fill('input[name="name"]', secondOrgName);

		// Submit the form
		await page.click('button[type="submit"]');

		// Should be redirected to the clients page or home
		await expect(page).toHaveURL(/\/org\/.*\/clients/, { timeout: 10000 });

		// Now test organization switching
		// Note: The exact UI for organization switching will depend on your application
		// This is a generic example, adjust selectors based on your implementation

		// Open organization switcher (often in the sidebar or navbar)
		await page.click('[aria-label="Switch organization"], [data-testid="org-switcher"]');

		// Select the first organization we created
		await page.click(`text=${testOrgName}`);

		// Verify the organization switch by checking for organization name in UI
		await expect(page.locator(`text=${testOrgName}`)).toBeVisible({ timeout: 5000 });
	});

	test('enforce permission boundaries between orgs', async ({ page }) => {
		// This test checks that a user can't access resources from another organization
		// We'll simulate this by generating a valid but wrong org ID in the URL

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', testEmail);
		await page.fill('input[name="password"]', testPassword);
		await page.click('button[type="submit"]');

		// Wait for successful login
		await expect(page).toHaveURL(/\/org\/new/, { timeout: 10000 });

		// Try to access a resource with a made-up organization name
		// This should fail or redirect to an error page
		const fakeOrgName = 'non-existent-org';
		await page.goto(`/org/${fakeOrgName}/settings`);

		// Verify we get an error or redirect
		// This could be a not found page, access denied message, or redirect to valid org
		await expect(page).toHaveURL(/\/(404|not-found|access-denied|org|auth)/, {
			timeout: 10000,
		});

		// We should not see any sensitive data from other organizations
		await expect(
			page.locator('text=API Keys, Billing Information, Payment Methods'),
		).not.toBeVisible();
	});
});
